<template>
  <div class="chart-title-wrapper">
    <img src="@/assets/icon/smalltitle.svg" class="title-bg">
    <h3 class="section-title">
      <slot>{{ title }}</slot>
    </h3>
    <!-- 额外内容插槽，用于筛选器等 -->
    <div v-if="$slots.extra" class="title-extra">
      <slot name="extra"></slot>
    </div>
  </div>
</template>

<script setup>
// 定义props
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
});
</script>

<style scoped lang="less">
.chart-title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 15px;
  height: 40px;
  width: 100%;
}

.title-bg{
  position: relative;
  z-index: 1;
  left: -10;
  top: 0;
  width: 100%;
  // width: min(280px, 70%); /* 响应式宽度：最大280px，最小70%容器宽度 */
  height: 40px; /* 固定高度，与容器一致 */
  // object-fit: contain;
}
.section-title {
  position: absolute;
  z-index: 2;
  color: #4fc3f7;
  font-size: 16px;
  font-weight: bold;
  left: 40px;
  text-align: left;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  white-space: nowrap;
  margin: 0;
  padding: 0;
  line-height: 40px;
}

.title-extra {
  position: relative;
  z-index: 2;
  margin-left: auto;
  margin-right: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 为筛选器等额外内容提供样式 */
.title-extra :deep(.title-filter-select) {
  background: rgba(0, 20, 50, 0.8);
  border: 1px solid rgba(79, 195, 247, 0.3);
  color: #4fc3f7;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  outline: none;
  
  &:focus {
    border-color: #4fc3f7;
    box-shadow: 0 0 5px rgba(79, 195, 247, 0.3);
  }
  
  option {
    background: rgba(0, 20, 50, 0.9);
    color: #4fc3f7;
  }
}
</style>
