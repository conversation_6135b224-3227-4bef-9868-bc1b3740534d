<template>
  <div class="district-building-stats">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="区内楼宇信息统计" />
        <div class="stats-grid">
          <div class="stat-card" v-for="stat in buildingStats" :key="stat.label">
            <div class="card-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-unit">{{ stat.unit }}</div>
            </div>
            <div class="wave-effect"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 接收父组件传递的选中区域信息和统计数据
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '长沙市',
      level: 'city',
      areaData: null
    })
  },
  summaryData: {
    type: Object,
    default: () => ({
      buildingNums: 0,
      investedArea: 0,
      vacantArea: 0,
      rentAmtAvg: 0
    })
  }
});

// 区域数据配置（根据表格数据）
const districtDataConfig = {
  '芙蓉区': {
    avgRent: 69.07,
    netAbsorption: 3.3,
    vacancyRate: 24.8,
    totalVolume: 1253826.34
  },
  '开福区': {
    avgRent: 64.56,
    netAbsorption: 0.1,
    vacancyRate: 26.4,
    totalVolume: 1350665.73
  },
  '天心区': {
    avgRent: 60.33,
    netAbsorption: 1.1,
    vacancyRate: 18.6,
    totalVolume: 620922.69
  },
  '望城区': {
    avgRent: 40.00,
    netAbsorption: 0,
    vacancyRate: 29.1,
    totalVolume: 39319.22
  },
  '浏阳新区': {
    avgRent: 76.24,
    netAbsorption: 3.6,
    vacancyRate: 21.5,
    totalVolume: 1133668.74
  },
  '雨花区': {
    avgRent: 66.09,
    netAbsorption: 2,
    vacancyRate: 17.3,
    totalVolume: 943124.43
  },
  '长沙县': {
    avgRent: 45.00,
    netAbsorption: 0.8,
    vacancyRate: 15.3,
    totalVolume: 174379.37
  }
};

// 动态楼宇统计数据
const buildingStats = computed(() => {
  const regionName = props.selectedRegion.regionName;
  const regionData = districtDataConfig[regionName] || districtDataConfig['芙蓉区'];

  return [
    {
      label: '平均租金',
      value: regionData.avgRent.toFixed(2),
      unit: '元/㎡·月'
    },
    {
      label: '净吸纳量',
      value: regionData.netAbsorption.toFixed(1),
      unit: '万㎡'
    },
    {
      label: '空置率',
      value: regionData.vacancyRate.toFixed(1),
      unit: '%'
    },
    {
      label: '总体量',
      value: (regionData.totalVolume / 10000).toFixed(2),
      unit: '万㎡'
    }
  ];
});
</script>

<style scoped lang="less">
.district-building-stats {
  height: 100%;
}

.border-box {
  height: 100%;
  background-size: 100% 100%;
  padding: 20px; /* 减少内边距 */
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
/* .title-bg 样式已移至ChartTitle组件 */

/* 原有的标题样式已移至ChartTitle组件 */

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px; /* 进一步减少卡片间距 */
  align-items: center;
  margin-top: 15px;
}

.stat-card {
  position: relative;
  padding: 8px; /* 减少内边距 */
  background: linear-gradient(135deg, rgba(79, 195, 247, 0.1) 0%, rgba(129, 212, 250, 0.05) 100%);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 70px; /* 减少最小高度 */
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
  border-color: rgba(79, 195, 247, 0.6);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.3;
}

.bt-icon-bg {
  width: 60%;
  height: 60%;
  object-fit: contain;
}

.card-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.stat-value {
  font-size: 22px; /* 减少字体大小 */
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 0 12px rgba(79, 195, 247, 0.6);
  font-family: 'Courier New', monospace;
  margin-bottom: 6px;
  animation: valueGlow 2s ease-in-out infinite alternate;
}

@keyframes valueGlow {
  0% {
    text-shadow: 0 0 15px rgba(79, 195, 247, 0.6);
  }
  100% {
    text-shadow: 0 0 25px rgba(79, 195, 247, 0.9), 0 0 35px rgba(129, 212, 250, 0.6);
  }
}

.stat-label {
  color: #e3f2fd;
  font-size: 11px; /* 减少字体大小 */
  font-weight: 500;
  opacity: 0.9;
  margin-bottom: 3px;
}

.stat-unit {
  color: #4fc3f7;
  font-size: 9px; /* 减少字体大小 */
  opacity: 0.8;
}

/* 波光特效 */
.wave-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(79, 195, 247, 0.2),
    rgba(129, 212, 250, 0.4),
    rgba(79, 195, 247, 0.2),
    transparent
  );
  animation: wave 3s ease-in-out infinite;
  z-index: 1;
}

@keyframes wave {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

.stat-card:hover .wave-effect {
  animation-duration: 1.5s;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .stats-grid {
    gap: 15px;
  }

  .stat-card {
    padding: 12px;
    min-height: 70px;
  }

  .stat-value {
    font-size: 20px;
  }

  .stat-label {
    font-size: 11px;
  }
}

@media (max-width: 1200px) {
  .stats-grid {
    gap: 12px;
  }

  .stat-card {
    padding: 10px;
    min-height: 60px;
  }

  .stat-value {
    font-size: 18px;
  }

  .stat-label {
    font-size: 10px;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stat-card {
    padding: 12px;
    min-height: 80px;
  }

  .stat-value {
    font-size: 18px;
  }
}
</style>
